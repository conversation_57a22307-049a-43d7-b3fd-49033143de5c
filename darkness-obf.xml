<config>
    <input>
        <jar in="target/777-FeatherAddons-1.0.jar" out="target/777-FeatherAddons-1.0-obf.jar"/>
    </input>

    <keep-names>
        <class access="public" name="me.darkness.featheraddons.Main">
            <method access="public" name="onEnable"/>
            <method access="public" name="onDisable"/>
            <method access="public" name="onLoad"/>
        </class>

        <class access="public" name="me.darkness.featheraddons.commands.FeatherAddonsCommand">
            <method access="public" name="onCommand"/>
            <method access="public" name="onTabComplete"/>
        </class>

        <class access="public" name="me.darkness.featheraddons.commands.SubCommand">
            <method access="public" name="getName"/>
            <method access="public" name="getPermission"/>
            <method access="public" name="getAliases"/>
            <method access="public" name="execute"/>
            <method access="public" name="getTabCompletions"/>
        </class>

        <class access="public" name="me.darkness.featheraddons.commands.subcommands.ModsSubCommand">
            <method access="public" name="getName"/>
            <method access="public" name="getPermission"/>
            <method access="public" name="getAliases"/>
            <method access="public" name="execute"/>
            <method access="public" name="getTabCompletions"/>
        </class>

        <class access="public" name="me.darkness.featheraddons.commands.subcommands.ReloadSubCommand">
            <method access="public" name="getName"/>
            <method access="public" name="getPermission"/>
            <method access="public" name="getAliases"/>
            <method access="public" name="execute"/>
            <method access="public" name="getTabCompletions"/>
        </class>

        <class access="public" name="me.darkness.featheraddons.listeners.FeatherPlayerListener">
            <method access="public" name="onPlayerQuit"/>
        </class>

        <class access="public" name="me.darkness.featheraddons.configuration.Configuration">
            <method access="public" name="load"/>
            <method access="public" name="getBlockedMods"/>
            <method access="public" name="isAutoBlockOnJoin"/>
            <method access="public" name="isDiscordEnabled"/>
            <method access="public" name="getServerIcon"/>
            <method access="public" name="getServerName"/>
            <method access="public" name="getDiscordDetails"/>
            <method access="public" name="getDiscordState"/>
            <method access="public" name="shouldShowPlayerCount"/>
            <method access="public" name="isServerBackgroundEnabled"/>
            <method access="public" name="getBackgroundImageUrl"/>
            <method access="public" name="getMessage"/>
            <method access="public" name="setValue"/>
        </class>

        <class access="public" name="me.darkness.featheraddons.configuration.Messages">
            <method access="public" name="colorize"/>
            <method access="public" name="get"/>
            <method access="public" name="send"/>
            <method access="public" name="sendRaw"/>
        </class>

        <class access="public" name="me.darkness.featheraddons.managers.ModManager">
            <method access="public" name="loadConfiguration"/>
            <method access="public" name="reload"/>
            <method access="public" name="applyBlockedMods"/>
            <method access="public" name="getFeatherPlayer"/>
            <method access="public" name="isValidModName"/>
            <method access="public" name="blockMod"/>
        </class>

        <class access="public" name="me.darkness.featheraddons.managers.DiscordManager">
            <method access="public" name="reload"/>
            <method access="public" name="isEnabled"/>
            <method access="public" name="updatePlayerActivity"/>
            <method access="public" name="clearPlayerActivity"/>
            <method access="public" name="shutdown"/>
        </class>

        <class access="public" name="me.darkness.featheraddons.managers.ServerBackgroundManager">
            <method access="public" name="reload"/>
            <method access="public" name="loadBackground"/>
        </class>
    </keep-names>

    <ignore-classes>
        <class template="class org.bukkit.**"/>
        <class template="class org.spigotmc.**"/>
        <class template="class net.md_5.**"/>

        <class template="class net.digitalingot.**"/>
        <class template="class me.clip.**"/>

        <class template="class com.mysql.**"/>
        <class template="class org.h2.**"/>
        <class template="class com.mongodb.**"/>
        <class template="class org.bson.**"/>

        <class template="class org.yaml.**"/>

        <class template="class java.**"/>
        <class template="class javax.**"/>
        <class template="class sun.**"/>
        <class template="class com.sun.**"/>

        <class template="class lombok.**"/>

        <class template="class com.google.**"/>
    </ignore-classes>

    <watermark key="777" value="777CODE"/>
    <property name="log-file" value="target/obfuscation.log"/>
    <property name="random-seed" value="maximum"/>
    <property name="string-encryption" value="enable"/>
    <property name="control-flow-obfuscation" value="enable"/>
    <property name="extensive-flow-obfuscation" value="maximum"/>
    <property name="packages-naming" value="123"/>
    <property name="classes-naming" value="iii"/>
    <property name="methods-naming" value="iii"/>
    <property name="fields-naming" value="iii"/>
    <property name="local-variables-naming" value="optimize"/>
    <property name="line-numbers" value="obfuscate"/>
    <property name="generics" value="remove"/>
    <property name="inner-classes" value="remove"/>
    <property name="member-reorder" value="enable"/>
</config>
