package me.darkness.featheraddons.configuration;

import me.darkness.featheraddons.Main;
import org.bukkit.ChatColor;
import org.bukkit.command.CommandSender;

public class Messages {

    private final Main plugin;

    public Messages(Main plugin) {
        this.plugin = plugin;
    }
    
    public String colorize(String message) {
        return ChatColor.translateAlternateColorCodes('&', message);
    }
    
    public String get(String key) {
        String message = plugin.getConfiguration().getMessage(key);
        return colorize(message);
    }

    public String get(String key, String placeholder, String value) {
        String message = get(key);
        return message.replace("{" + placeholder + "}", value);
    }
    
    public void send(CommandSender sender, String key) {
        sender.sendMessage(get(key));
    }
    
    public void send(CommandSender sender, String key, String placeholder, String value) {
        sender.sendMessage(get(key, placeholder, value));
    }

    public void sendRaw(CommandSender sender, String message) {
        sender.sendMessage(colorize(message));
    }
}
