package me.darkness.featheraddons.configuration;

import me.darkness.featheraddons.Main;
import org.bukkit.configuration.file.FileConfiguration;

import java.util.List;

public class Configuration {

    private final Main plugin;
    private FileConfiguration config;

    public Configuration(Main plugin) {
        this.plugin = plugin;
    }

    public void load() {
        this.config = plugin.getConfig();

        validate();
    }

    private void validate() {
        if (!config.contains("mods")) {
            plugin.getLogger().warning("Nie znaleziono sekcji 'mods' w config.yml");
        }
        
        if (!config.contains("discord")) {
            plugin.getLogger().warning("Nie znaleziono sekcji 'discord' w config.yml");
        }
        
        if (!config.contains("server-background")) {
            plugin.getLogger().warning("Nie znaleziono sekcji 'server-background' w config.yml");
        }
    }

    public List<String> getBlockedMods() {
        return config.getStringList("mods.blocked");
    }
    
    public boolean isAutoBlockOnJoin() {
        return true;
    }

    public boolean isDiscordEnabled() {
        return config.getBoolean("discord.enabled", true);
    }
    
    public String getServerIcon() {
        return config.getString("discord.server-icon", "");
    }
    
    public String getServerName() {
        return config.getString("discord.server-name", "wiadomosc3");
    }
    
    public String getDiscordDetails() {
        return config.getString("discord.details", "wiadomosc2");
    }
    
    public String getDiscordState() {
        return config.getString("discord.state", "wiadomosc1");
    }
    
    public boolean shouldShowPlayerCount() {
        return config.getBoolean("discord.show-player-count", true);
    }

    public boolean isServerBackgroundEnabled() {
        return config.getBoolean("server-background.enabled", true);
    }

    public String getBackgroundImageUrl() {
        return config.getString("server-background.image-url", "");
    }
    
    public String getMessage(String key) {
        return config.getString("messages." + key, "brak wiadomosci: " + key);
    }

    public void setValue(String path, Object value) {
        config.set(path, value);
        plugin.saveConfig();
    }
}
