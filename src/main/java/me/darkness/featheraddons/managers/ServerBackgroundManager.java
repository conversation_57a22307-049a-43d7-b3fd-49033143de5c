package me.darkness.featheraddons.managers;

import me.darkness.featheraddons.Main;
import net.digitalingot.feather.serverapi.api.FeatherAPI;
import net.digitalingot.feather.serverapi.api.meta.ServerListBackground;
import net.digitalingot.feather.serverapi.api.meta.ServerListBackgroundFactory;
import org.bukkit.Bukkit;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URL;

public class ServerBackgroundManager {

    private final Main plugin;
    private ServerListBackground currentBackground;

    public ServerBackgroundManager(Main plugin) {
        this.plugin = plugin;

        if (plugin.getConfiguration().isServerBackgroundEnabled()) {
            String imageUrl = plugin.getConfiguration().getBackgroundImageUrl();
            if (!imageUrl.isEmpty()) {
                Bukkit.getScheduler().runTaskAsynchronously(plugin, this::loadBackground);
            }
        }
    }
    
    public void reload() {
        if (plugin.getConfiguration().isServerBackgroundEnabled()) {
            String imageUrl = plugin.getConfiguration().getBackgroundImageUrl();
            if (!imageUrl.isEmpty()) {
                loadBackground();
            }
        }
    }
    
    public void loadBackground() {
        if (!plugin.getConfiguration().isServerBackgroundEnabled()) {
            return;
        }

        String imageUrl = plugin.getConfiguration().getBackgroundImageUrl();
        if (imageUrl == null || imageUrl.isEmpty()) {
            plugin.getLogger().info("Brak obrazu tla serwera w konfiguracji");
            return;
        }
        
        try {
            loadBackgroundFromUrl(imageUrl);
        } catch (Exception e) {
            plugin.getLogger().severe("Blad przy ustawianiu tla serwera: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void loadBackgroundFromUrl(String imageUrl) throws Exception {
        try {
            URL url = new URL(imageUrl);
            byte[] imageBytes = downloadImageFromUrl(url);

            ServerListBackgroundFactory factory = FeatherAPI.getMetaService().getServerListBackgroundFactory();
            ServerListBackground background = factory.fromBytes(imageBytes);

            FeatherAPI.getMetaService().setServerListBackground(background);
            this.currentBackground = background;

        } catch (Exception e) {
            throw new RuntimeException("Blad przy ustawianiu tla serwera: " + e.getMessage(), e);
        }
    }

    private byte[] downloadImageFromUrl(URL url) throws Exception {
        try (InputStream inputStream = url.openStream();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            return outputStream.toByteArray();
        }
    }
}
