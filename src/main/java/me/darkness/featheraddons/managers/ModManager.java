package me.darkness.featheraddons.managers;

import me.darkness.featheraddons.Main;
import net.digitalingot.feather.serverapi.api.FeatherAPI;
import net.digitalingot.feather.serverapi.api.model.FeatherMod;
import net.digitalingot.feather.serverapi.api.player.FeatherPlayer;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

public class ModManager {

    private final Main plugin;
    private List<String> defaultBlockedMods;

    public ModManager(Main plugin) {
        this.plugin = plugin;
        loadConfiguration();
    }

    public void loadConfiguration() {
        this.defaultBlockedMods = new ArrayList<>(plugin.getConfiguration().getBlockedMods());
    }

    public void reload() {
        loadConfiguration();
    }

    public void applyBlockedMods(FeatherPlayer featherPlayer) {
        if (!plugin.getConfiguration().isAutoBlockOnJoin() || defaultBlockedMods.isEmpty()) {
            return;
        }

        List<FeatherMod> modsToBlock = defaultBlockedMods.stream()
                .map(FeatherMod::new)
                .collect(Collectors.toList());

        featherPlayer.blockMods(modsToBlock);
    }

    public FeatherPlayer getFeatherPlayer(UUID playerUuid) {
        return FeatherAPI.getPlayerService().getPlayer(playerUuid);
    }

    public boolean isValidModName(String modName) {
        return modName != null && modName.matches("^[a-z0-9_]+$");
    }

    public void blockMod(String modName) {
        Collection<FeatherPlayer> featherPlayers = FeatherAPI.getPlayerService().getPlayers();
        FeatherMod mod = new FeatherMod(modName);

        for (FeatherPlayer featherPlayer : featherPlayers) {
            try {
                featherPlayer.blockMods(List.of(mod));
            } catch (Exception e) {
            }
        }
    }

    public void unblockMod(String modName) {
        Collection<FeatherPlayer> featherPlayers = FeatherAPI.getPlayerService().getPlayers();
        FeatherMod mod = new FeatherMod(modName);

        for (FeatherPlayer featherPlayer : featherPlayers) {
            try {
                featherPlayer.unblockMods(List.of(mod));
            } catch (Exception e) {
            }
        }
    }
}
