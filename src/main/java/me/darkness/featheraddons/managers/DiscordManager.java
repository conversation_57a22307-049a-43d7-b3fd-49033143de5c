package me.darkness.featheraddons.managers;

import me.darkness.featheraddons.Main;
import net.digitalingot.feather.serverapi.api.FeatherAPI;
import net.digitalingot.feather.serverapi.api.player.FeatherPlayer;
import net.digitalingot.feather.serverapi.api.meta.DiscordActivity;
import org.bukkit.Bukkit;
import org.bukkit.scheduler.BukkitTask;

import java.util.Collection;

public class DiscordManager {

    private final Main plugin;
    private BukkitTask updateTask;
    private DiscordActivity defaultActivity;

    public DiscordManager(Main plugin) {
        this.plugin = plugin;
        loadConfiguration();
        startUpdateTask();
    }
    
    public void loadConfiguration() {
        if (!plugin.getConfiguration().isDiscordEnabled()) {
            return;
        }
        createDefaultActivity();
    }

    public void reload() {
        stopUpdateTask();
        loadConfiguration();
        startUpdateTask();
    }

    public void shutdown() {
        stopUpdateTask();
        clearAllActivities();
    }

    private void createDefaultActivity() {
        if (!plugin.getConfiguration().isDiscordEnabled()) {
            return;
        }

        DiscordActivity.Builder builder = DiscordActivity.builder();

        String serverIcon = plugin.getConfiguration().getServerIcon();
        if (!serverIcon.isEmpty()) {
            builder.withImage(serverIcon);
        }

        String serverName = plugin.getConfiguration().getServerName();
        if (!serverName.isEmpty()) {
            builder.withImageText(serverName);
        }

        String details = plugin.getConfiguration().getDiscordDetails();
        if (!details.isEmpty()) {
            builder.withDetails(details);
        } else if (!serverName.isEmpty()) {
            builder.withDetails(serverName);
        }

        String state = plugin.getConfiguration().getDiscordState();
        if (!state.isEmpty()) {
            builder.withState(state);
        }

        if (plugin.getConfiguration().shouldShowPlayerCount()) {
            int onlinePlayers = Bukkit.getOnlinePlayers().size();
            int maxPlayers = Bukkit.getMaxPlayers();

            if (onlinePlayers < 1) {
                onlinePlayers = 1;
            }
            if (maxPlayers < 1) {
                maxPlayers = 1;
            }

            builder.withPartySize(onlinePlayers, maxPlayers);
        }

        builder.withStartTimestamp(System.currentTimeMillis());

        this.defaultActivity = builder.build();
    }
    
    private void startUpdateTask() {
        if (!plugin.getConfiguration().isDiscordEnabled()) {
            return;
        }

        updateTask = Bukkit.getScheduler().runTaskTimerAsynchronously(plugin, () -> {
            updateAllActivities();
        }, 600L, 600L);
    }
    
    private void stopUpdateTask() {
        if (updateTask != null) {
            updateTask.cancel();
            updateTask = null;
        }
    }
    
    public void updateAllActivities() {
        if (!plugin.getConfiguration().isDiscordEnabled() || defaultActivity == null) {
            return;
        }

        createDefaultActivity();
        
        Collection<FeatherPlayer> featherPlayers = FeatherAPI.getPlayerService().getPlayers();
        
        for (FeatherPlayer player : featherPlayers) {
            try {
                FeatherAPI.getMetaService().updateDiscordActivity(player, defaultActivity);
            } catch (Exception e) {
            }
        }
    }
    
    public void updatePlayerActivity(FeatherPlayer player) {
        if (!plugin.getConfiguration().isDiscordEnabled() || defaultActivity == null) {
            return;
        }
        
        try {
            FeatherAPI.getMetaService().updateDiscordActivity(player, defaultActivity);
        } catch (Exception e) {
        }
    }
    
    public void clearPlayerActivity(FeatherPlayer player) {
        try {
            FeatherAPI.getMetaService().clearDiscordActivity(player);
        } catch (Exception e) {
        }
    }
    
    public void clearAllActivities() {
        Collection<FeatherPlayer> featherPlayers = FeatherAPI.getPlayerService().getPlayers();
        
        for (FeatherPlayer player : featherPlayers) {
            clearPlayerActivity(player);
        }
    }

    public boolean isEnabled() {
        return plugin.getConfiguration().isDiscordEnabled();
    }
}
