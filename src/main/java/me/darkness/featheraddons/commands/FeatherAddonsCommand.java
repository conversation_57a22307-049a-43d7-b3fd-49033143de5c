package me.darkness.featheraddons.commands;

import me.darkness.featheraddons.Main;
import me.darkness.featheraddons.commands.subcommands.ModsSubCommand;
import me.darkness.featheraddons.commands.subcommands.ReloadSubCommand;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class FeatherAddonsCommand implements CommandExecutor, TabCompleter {

    private final Main plugin;
    private final List<SubCommand> subCommands;

    public FeatherAddonsCommand(Main plugin) {
        this.plugin = plugin;
        this.subCommands = new ArrayList<>();

        registerSubCommands();
    }
    
    private void registerSubCommands() {
        subCommands.add(new ReloadSubCommand(plugin));
        subCommands.add(new ModsSubCommand(plugin));
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        String subCommandName = args[0].toLowerCase();

        for (SubCommand subCommand : subCommands) {
            if (subCommand.getName().equalsIgnoreCase(subCommandName) || 
                subCommand.getAliases().contains(subCommandName)) {

                if (!sender.hasPermission(subCommand.getPermission())) {
                    plugin.getMessages().send(sender, "no-permission");
                    return true;
                }

                String[] subArgs = Arrays.copyOfRange(args, 1, args.length);
                subCommand.execute(sender, subArgs);
                return true;
            }
        }

        plugin.getMessages().send(sender, "invalid-command");
        return true;
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();
        
        if (args.length == 1) {
            for (SubCommand subCommand : subCommands) {
                if (sender.hasPermission(subCommand.getPermission())) {
                    completions.add(subCommand.getName());
                    completions.addAll(subCommand.getAliases());
                }
            }

            return completions.stream()
                    .filter(completion -> completion.toLowerCase().startsWith(args[0].toLowerCase()))
                    .collect(Collectors.toList());
        }
        
        if (args.length > 1) {
            String subCommandName = args[0].toLowerCase();
            
            for (SubCommand subCommand : subCommands) {
                if ((subCommand.getName().equalsIgnoreCase(subCommandName) || 
                     subCommand.getAliases().contains(subCommandName)) &&
                    sender.hasPermission(subCommand.getPermission())) {
                    
                    String[] subArgs = Arrays.copyOfRange(args, 1, args.length);
                    return subCommand.getTabCompletions(sender, subArgs);
                }
            }
        }
        
        return completions;
    }
}
