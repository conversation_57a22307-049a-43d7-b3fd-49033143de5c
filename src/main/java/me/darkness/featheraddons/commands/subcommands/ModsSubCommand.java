package me.darkness.featheraddons.commands.subcommands;

import me.darkness.featheraddons.Main;
import me.darkness.featheraddons.commands.SubCommand;
import org.bukkit.Bukkit;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

public class ModsSubCommand implements SubCommand {

    private final Main plugin;
    private final List<String> commonMods = Arrays.asList(
            "perspective", "zoom", "timer", "fps", "coordinates", "keystrokes",
            "cps", "ping", "armor_status", "item_physics", "motion_blur",
            "brightness", "saturation", "crosshair", "waypoints", "minimap"
    );
    
    public ModsSubCommand(Main plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public String getName() {
        return "mods";
    }
    
    @Override
    public String getPermission() {
        return "featheraddons.mods";
    }
    
    @Override
    public List<String> getAliases() {
        return new ArrayList<>();
    }
    
    @Override
    public void execute(CommandSender sender, String[] args) {
        if (args.length < 1) {
            return;
        }

        String action = args[0].toLowerCase();

        if ("list".equals(action)) {
            executeListAction(sender);
            return;
        }

        String modName = args[1].toLowerCase();

        if (!plugin.getModManager().isValidModName(modName)) {
            plugin.getMessages().send(sender, "mod-not-found", "mod", modName);
            return;
        }
        switch (action) {
            case "block":
                executeBlockAction(sender, modName);
                break;
            case "unblock":
                executeUnblockAction(sender, modName);
                break;
            default:
                break;
        }
    }
    
    private void executeBlockAction(CommandSender sender, String modName) {
        List<String> blockedMods = new ArrayList<>(plugin.getConfiguration().getBlockedMods());
        if (!blockedMods.contains(modName)) {
            blockedMods.add(modName);
            plugin.getConfiguration().setValue("mods.blocked", blockedMods);
            plugin.getMessages().send(sender, "mod-blocked", "mod", modName);

            plugin.getModManager().reload();

            plugin.getModManager().blockMod(modName);
        } else {
            plugin.getMessages().send(sender, "mod-already-blocked", "mod", modName);
        }
    }
    
    private void executeUnblockAction(CommandSender sender, String modName) {
        List<String> blockedMods = new ArrayList<>(plugin.getConfiguration().getBlockedMods());
        if (blockedMods.contains(modName)) {
            blockedMods.remove(modName);
            plugin.getConfiguration().setValue("mods.blocked", blockedMods);
            plugin.getMessages().send(sender, "mod-unblocked", "mod", modName);

            plugin.getModManager().reload();

            plugin.getModManager().unblockMod(modName);
        } else {
            plugin.getMessages().send(sender, "mod-not-blocked", "mod", modName);
        }
    }

    private void executeListAction(CommandSender sender) {
        List<String> blockedMods = plugin.getConfiguration().getBlockedMods();
        if (blockedMods.isEmpty()) {
            plugin.getMessages().send(sender, "no-blocked-mods");
        } else {
            String modsList = String.join(", ", blockedMods);
            plugin.getMessages().send(sender, "blocked-mods-list", "mods", modsList);
        }
    }

    @Override
    public List<String> getTabCompletions(CommandSender sender, String[] args) {
        List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            completions.addAll(Arrays.asList("block", "unblock", "list"));
        } else if (args.length == 2) {
            String action = args[0].toLowerCase();
            String input = args[1].toLowerCase();

            if ("block".equals(action)) {
                List<String> blockedMods = plugin.getConfiguration().getBlockedMods();
                completions.addAll(commonMods.stream()
                        .filter(mod -> !blockedMods.contains(mod) && mod.startsWith(input))
                        .collect(Collectors.toList()));
            } else if ("unblock".equals(action)) {
                List<String> blockedMods = plugin.getConfiguration().getBlockedMods();
                completions.addAll(blockedMods.stream()
                        .filter(mod -> mod.startsWith(input))
                        .collect(Collectors.toList()));
            }
        }

        return completions;
    }
}
