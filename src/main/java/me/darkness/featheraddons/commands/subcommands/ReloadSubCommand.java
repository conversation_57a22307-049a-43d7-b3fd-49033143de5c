package me.darkness.featheraddons.commands.subcommands;

import me.darkness.featheraddons.Main;
import me.darkness.featheraddons.commands.SubCommand;
import org.bukkit.command.CommandSender;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ReloadSubCommand implements SubCommand {

    private final Main plugin;

    public ReloadSubCommand(Main plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public String getName() {
        return "reload";
    }
    
    @Override
    public String getPermission() {
        return "featheraddons.reload";
    }
    
    @Override
    public List<String> getAliases() {
        return new ArrayList<>();
    }
    
    @Override
    public void execute(CommandSender sender, String[] args) {
        try {
            plugin.reloadConfiguration();
            plugin.getMessages().send(sender, "config-reloaded");
        } catch (Exception e) {
        }
    }
    
    @Override
    public List<String> getTabCompletions(CommandSender sender, String[] args) {
        return new ArrayList<>();
    }
}
