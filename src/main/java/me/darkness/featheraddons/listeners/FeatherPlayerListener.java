package me.darkness.featheraddons.listeners;

import me.darkness.featheraddons.Main;
import net.digitalingot.feather.serverapi.api.FeatherAPI;
import net.digitalingot.feather.serverapi.api.player.FeatherPlayer;
import net.digitalingot.feather.serverapi.api.event.player.PlayerHelloEvent;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerQuitEvent;

public class FeatherPlayerListener implements Listener {

    private final Main plugin;

    public FeatherPlayerListener(Main plugin) {
        this.plugin = plugin;
        registerFeatherEvents();
    }

    private void registerFeatherEvents() {
        FeatherAPI.getEventService().subscribe(PlayerHelloEvent.class, this::onPlayerHello);
    }
    
    private void onPlayerHello(PlayerHelloEvent event) {
        FeatherPlayer featherPlayer = event.getPlayer();
        Player bukkitPlayer = Bukkit.getPlayer(featherPlayer.getUniqueId());
        
        if (bukkitPlayer == null) {
            return;
        }
        
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            plugin.getModManager().applyBlockedMods(featherPlayer);
        }, 20L);

        if (plugin.getDiscordManager().isEnabled()) {
            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                plugin.getDiscordManager().updatePlayerActivity(featherPlayer);
            }, 40L);
        }
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        FeatherPlayer featherPlayer = plugin.getModManager().getFeatherPlayer(player.getUniqueId());
        
        if (featherPlayer != null) {
            if (plugin.getDiscordManager().isEnabled()) {
                plugin.getDiscordManager().clearPlayerActivity(featherPlayer);
            }
        }
    }
}
